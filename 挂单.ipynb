import pandas as pd

trades_path = r'E:\刷交易量\datasets\binance_trades_2025-08-01_BTCFDUSD.csv.gz'
orderbook_path = r'E:\刷交易量\datasets\binance_book_snapshot_25_2025-08-01_BTCFDUSD.csv.gz'

trades_df = pd.read_csv(trades_path, compression='gzip')
orderbook_df = pd.read_csv(orderbook_path, compression='gzip')

print("Trades head:")
print(trades_df.head())

print("\nOrderbook head:")
print(orderbook_df.head())

# 计算 orderbook_df 的 timestamp 列的时间间隔（单位：微秒、秒）
orderbook_df['timestamp_diff_us'] = orderbook_df['timestamp'].diff()
orderbook_df['timestamp_diff_s'] = orderbook_df['timestamp_diff_us'] / 1e6
print(orderbook_df[['timestamp', 'timestamp_diff_us', 'timestamp_diff_s']].head(10))

import numpy as np

# 计算 mid price
orderbook_df['mid_price'] = (orderbook_df['bids[0].price'] + orderbook_df['asks[0].price']) / 2

# 计算 OBI (Order Book Imbalance)，这里用前25档
bid_size_cols = [f'bids[{i}].amount' for i in range(25)]
ask_size_cols = [f'asks[{i}].amount' for i in range(25)]
orderbook_df['bid_vol'] = orderbook_df[bid_size_cols].sum(axis=1)
orderbook_df['ask_vol'] = orderbook_df[ask_size_cols].sum(axis=1)
orderbook_df['obi'] = (orderbook_df['bid_vol'] - orderbook_df['ask_vol']) / (orderbook_df['bid_vol'] + orderbook_df['ask_vol'] + 1e-9)

# 设置桶大小
bucket_size = 100
orderbook_df['obi_smooth'] = orderbook_df['obi'].rolling(window=bucket_size, min_periods=1).mean()

# 用第一个小时3s后的价格来拟合c1
from scipy.optimize import minimize

# 取第一个小时的数据
one_hour_us = 60 * 60 * 1e6 * 24
start_ts = orderbook_df['timestamp'].iloc[0]
end_ts = start_ts + one_hour_us
mask_1h = (orderbook_df['timestamp'] >= start_ts) & (orderbook_df['timestamp'] < end_ts)
df_1h = orderbook_df.loc[mask_1h].reset_index(drop=True)

# 3秒后的mid_price
shift_us = 3 * 1e6
df_1h['mid_price_3s_after'] = np.interp(
    df_1h['timestamp'] + shift_us,
    df_1h['timestamp'],
    df_1h['mid_price']
)

# 去掉最后3秒无法对齐的部分
valid_mask = ~np.isnan(df_1h['mid_price_3s_after'])
df_fit = df_1h.loc[valid_mask].copy()

def loss_fn(c1):
    pred = df_fit['mid_price'] + c1 * df_fit['obi_smooth']
    return np.mean((pred - df_fit['mid_price_3s_after'])**2)

res = minimize(loss_fn, x0=[5], method='Nelder-Mead')
c1 = 5
# 计算 adjusted mid price
orderbook_df['adjusted_mid_price'] = orderbook_df['mid_price'] + c1 * orderbook_df['obi_smooth']
import matplotlib.pyplot as plt
print(orderbook_df['obi'].describe())
plt.figure(figsize=(10,5))
plt.hist(orderbook_df['obi'], bins=100, color='skyblue', edgecolor='k')
plt.title('OBI分布直方图')
plt.xlabel('OBI')
plt.ylabel('频数')
plt.show()

c1








import matplotlib.pyplot as plt

# 先绘制1小时的数据
one_hour_us = 60 * 60 * 1e6 * 2
start_ts = orderbook_df['timestamp'].iloc[0]
end_ts = start_ts + one_hour_us
mask_1h = (orderbook_df['timestamp'] >= start_ts) & (orderbook_df['timestamp'] < end_ts)
df_1h = orderbook_df.loc[mask_1h].reset_index(drop=True)

fig, ax = plt.subplots(figsize=(16, 6))

x = df_1h['timestamp']
y = df_1h['adjusted_mid_price']
obi = df_1h['obi']

x_plot = (x - x.iloc[0]) / 1e6

ax.plot(x_plot, y, color='black', linewidth=1.5, label='adjusted_mid_price')

# for i in range(len(df_1h) - 1):
#     color = 'lightgreen' if obi.iloc[i] >= 0 else 'lightcoral'
#     ax.axvspan(x_plot.iloc[i], x_plot.iloc[i+1], color=color, alpha=0.3, linewidth=0)

ax.set_xlabel('Time (s)')
ax.set_ylabel('Adjusted Mid Price')
ax.set_title('Adjusted Mid Price with OBI Background (First 1 Hour)')
ax.legend()
plt.tight_layout()
plt.show()

# 根据调整后的价格反向进行多空回测（成交价用midprice）

# 策略：OBI跨越阈值分位数时反向开仓
df = df_1h.copy()
df['obi_prev'] = df['obi'].shift(1)
df['signal'] = 0

# 计算OBI的50分位数阈值
obi_threshold = df['obi'].abs().quantile(0)

# OBI从负变正且绝对值大于阈值，做空；OBI从正变负且绝对值大于阈值，做多
df.loc[(df['obi_prev'] < 0) & (df['obi'] >= 0) & (df['obi'].abs() > obi_threshold), 'signal'] = -1  
df.loc[(df['obi_prev'] > 0) & (df['obi'] <= 0) & (df['obi'].abs() > obi_threshold), 'signal'] = 1   

# 持仓信号
df['position'] = df['signal'].replace(to_replace=0, method='ffill').fillna(0)

# 计算收益（用midprice成交，假设无滑点无手续费）
df['mid_price'] = df['mid_price']
df['mid_price_shift'] = df['mid_price'].shift(1)

# 多头是下一个空信号转空仓，空头是下一个多信号转多仓

df['next_signal_mid'] = np.nan

signal_indices = df.index[df['signal'] != 0].to_list()
signals = df.loc[signal_indices, 'signal'].values

# 向量化处理，利用pandas的shift和where
next_signal_idx = np.full(len(df), df.index[-1])
signal_pos = np.where(df['signal'] != 0)[0]
signal_vals = df['signal'].values[signal_pos]

# 先构建下一个反向信号的索引
next_idx = np.full(len(signal_pos), df.index[-1])

# 找到下一个反向信号的位置
for i in range(len(signal_pos)):
    entry_signal = signal_vals[i]
    if entry_signal == -1:
        # 多头，找下一个signal==1
        mask = signal_vals[i+1:] == 1
    elif entry_signal == 1:
        # 空头，找下一个signal==-1
        mask = signal_vals[i+1:] == -1
    else:
        mask = np.zeros(len(signal_vals[i+1:]), dtype=bool)
    if mask.any():
        next_idx[i] = df.index[signal_pos[i+1:][mask][0]]
    else:
        next_idx[i] = df.index[-1]

# 将结果写入df
df.loc[df['signal'] != 0, 'next_signal_mid'] = df.loc[next_idx, 'mid_price'].values

# 计算long_ret和short_ret，只在开仓点有值，其余为NaN
long_ret = (df['next_signal_mid'] - df['mid_price']) / df['mid_price']
short_ret = (df['mid_price'] - df['next_signal_mid']) / df['mid_price']

position_shift = df['position'].shift(1).fillna(0)
df['strategy_ret'] = 0
df.loc[position_shift > 0, 'strategy_ret'] = long_ret[position_shift > 0]
df.loc[position_shift < 0, 'strategy_ret'] = short_ret[position_shift < 0]

# 计算每笔交易的收益
trade_entries = df[df['signal'] != 0].copy()
trade_entries['trade_type'] = trade_entries['signal'].map({1: 'long', -1: 'short'})

trade_entries['entry_price'] = trade_entries['mid_price']
trade_entries['entry_time'] = trade_entries['timestamp']

# 计算平仓点（下一个信号或最后一根bar）
trade_entries['exit_idx'] = list(trade_entries.index[1:]) + [df.index[-1]]
trade_entries['exit_price'] = df.loc[trade_entries['exit_idx'], 'mid_price'].values
trade_entries['exit_time'] = df.loc[trade_entries['exit_idx'], 'timestamp'].values

# 修正收益计算
trade_entries['pnl'] = np.where(
    trade_entries['signal'] == 1,  # 做多
    (trade_entries['exit_price'] - trade_entries['entry_price']) / trade_entries['entry_price'],
    (trade_entries['entry_price'] - trade_entries['exit_price']) / trade_entries['entry_price']   # 做空
)

# 输出统计
print(f"总交易次数: {len(trade_entries)}")
print("每笔交易收益:")
print(trade_entries[['entry_time', 'exit_time', 'trade_type', 'entry_price', 'exit_price', 'pnl']])

# 计算资金曲线
df['equity_curve'] = (1 + df['strategy_ret']).cumprod()

# 手动画资金曲线，同时绘制价格走势曲线
plt.figure(figsize=(16,6))
x_equity = (df['timestamp'] - df['timestamp'].iloc[0]) / 1e6
y_equity = df['equity_curve']
y_price = df['mid_price'] / df['mid_price'].iloc[0]  # 归一化价格曲线
plt.plot(x_equity, y_equity, label='Equity Curve (OBI reverse)', color='blue')
plt.plot(x_equity, y_price, label='Normalized Mid Price', color='orange', alpha=0.7)

plt.xlabel('Time (s)')
plt.ylabel('Value')
plt.title('Equity Curve & Price Curve of OBI Reverse Strategy (midprice)')
plt.legend()
plt.tight_layout()
plt.show()

# 挂单测试：在上下500个tick挂买单和卖单，订单挂3秒，trade区间最高/最低判断成交（加速版）
import matplotlib.pyplot as plt

trades_df = pd.read_csv(trades_path, compression='gzip')
order_book_col = 'adjusted_mid_price'
tick_size = 0.01
order_offset = 500 * tick_size

# 预先生成所有挂单，每300ms挂一个单，也就是n//3
n = len(df)
order_idx = np.arange(n)
now_time = df['timestamp'].values
mid = df[order_book_col].values
buy_price = mid - order_offset
sell_price = mid + order_offset

orders_df = pd.DataFrame({
    'type': np.tile(['buy', 'sell'], n),
    'price': np.concatenate([buy_price, sell_price]),
    'place_time': np.concatenate([now_time, now_time]),
    'expire_time': np.concatenate([now_time + 10_000_000, now_time + 10_000_000]),
    'status': 'open',
    'place_idx': np.concatenate([order_idx, order_idx])
})

# trades_df: timestamp, price
trade_times = trades_df['timestamp'].values
trade_prices = trades_df['price'].values

# 用searchsorted加速trade区间定位
order_place_time = orders_df['place_time'].values
order_expire_time = orders_df['expire_time'].values
order_type = orders_df['type'].values
order_price = orders_df['price'].values

left_idx = np.searchsorted(trade_times, order_place_time, side='left')
right_idx = np.searchsorted(trade_times, order_expire_time, side='right')

exec_time = np.full(len(orders_df), np.nan)
exec_idx = np.full(len(orders_df), np.nan)
exec_price = np.full(len(orders_df), np.nan)
status = np.array(['open'] * len(orders_df), dtype=object)

for i in range(len(orders_df)):
    l, r = left_idx[i], right_idx[i]
    if l >= r:
        status[i] = 'expired'
        continue
    window_prices = trade_prices[l:r]
    window_times = trade_times[l:r]
    if order_type[i] == 'buy':
        idxs = np.where(window_prices < order_price[i])[0]
        if idxs.size > 0:
            fill_idx = idxs[0]
            exec_time[i] = window_times[fill_idx]
            exec_idx[i] = orders_df.iloc[i]['place_idx']
            exec_price[i] = window_prices[fill_idx]
            status[i] = 'filled'
        else:
            status[i] = 'expired'
    else:  # sell
        idxs = np.where(window_prices > order_price[i])[0]
        if idxs.size > 0:
            fill_idx = idxs[0]
            exec_time[i] = window_times[fill_idx]
            exec_idx[i] = orders_df.iloc[i]['place_idx']
            exec_price[i] = window_prices[fill_idx]
            status[i] = 'filled'
        else:
            status[i] = 'expired'

orders_df['exec_time'] = exec_time
orders_df['exec_idx'] = exec_idx
orders_df['exec_price'] = exec_price
orders_df['status'] = status

# 修复后的库存计算逻辑
pair_count = n
pair_results = np.full(pair_count, 'none', dtype=object)
position = 0
pnl = 0
pnl_list = []
position_list = []
open_long_price = []
open_short_price = []

# 先按下单时间排序
orders_df_sorted = orders_df.sort_values(by='place_time').reset_index(drop=True)

# 假设每次成交数量为qty，库存惩罚系数为lambda_penalty
qty = 1
lambda_penalty = 0.1  # 库存惩罚系数，可调整

# 记录每一步的position用于回看10s前的库存
position_history = []
time_history = []

for i in range(pair_count):
    buy_order = orders_df_sorted.iloc[2*i]
    sell_order = orders_df_sorted.iloc[2*i+1]
    cur_price = df[order_book_col].iloc[i]
    buy_filled = buy_order['status'] == 'filled'
    sell_filled = sell_order['status'] == 'filled'

    # 获取当前挂单对的时间
    cur_time = buy_order['place_time']
    # 查找10秒前的库存
    # 如果place_time是int或float（如时间戳），直接减10
    if np.issubdtype(type(cur_time), np.integer) or np.issubdtype(type(cur_time), np.floating):
        lookback_time = cur_time - 10
    else:
        lookback_time = cur_time - pd.Timedelta(seconds=10)
    # 找到距离lookback_time最近但不晚于lookback_time的index
    lookback_idx = None
    for j in range(i-1, -1, -1):
        prev_time = time_history[j]
        if prev_time <= lookback_time:
            lookback_idx = j
            break
    if lookback_idx is not None:
        position_10s_ago = position_history[lookback_idx]
    else:
        position_10s_ago = 0

    # 库存惩罚调整成交数量，使用10s前的库存
    buy_qty = qty * (1 - lambda_penalty * position_10s_ago)
    sell_qty = qty * (1 + lambda_penalty * position_10s_ago)
    # 限制最小成交量为0
    buy_qty = max(buy_qty, 0)
    sell_qty = max(sell_qty, 0)
    if buy_filled and sell_filled:
        # 双边成交，按库存惩罚调整后的数量
        trade_qty = min(buy_qty, sell_qty)
        pnl += trade_qty * (sell_order['exec_price'] - buy_order['exec_price']) / buy_order['exec_price']
        pair_results[i] = 'both'
        # position不变
    elif buy_filled:
        position += buy_qty
        open_long_price.extend([buy_order['exec_price']] * int(round(buy_qty)))
        pair_results[i] = 'buy_only'
    elif sell_filled:
        position -= sell_qty
        open_short_price.extend([sell_order['exec_price']] * int(round(sell_qty)))
        pair_results[i] = 'sell_only'
    # 浮动pnl计算
    float_long_pnl = np.sum((cur_price - np.array(open_long_price)) / np.array(open_long_price)) if open_long_price else 0
    float_short_pnl = np.sum((np.array(open_short_price) - cur_price) / np.array(open_short_price)) if open_short_price else 0
    total_pnl = pnl + float_long_pnl + float_short_pnl
    pnl_list.append(total_pnl)
    position_list.append(position)
    position_history.append(position)
    time_history.append(cur_time)

both_count = np.sum(pair_results == 'both')
buy_only_count = np.sum(pair_results == 'buy_only')
sell_only_count = np.sum(pair_results == 'sell_only')
none_count = np.sum(pair_results == 'none')
print(f"总挂单对数: {pair_count}")
print(f"双边成交数: {both_count}，概率: {both_count/pair_count:.2%}")
print(f"单边买成交数: {buy_only_count}，单边卖成交数: {sell_only_count}，都未成交数: {none_count}")
print(f"最终持仓: {position}")
print(f"累计pnl: {pnl:.6f}")
buy_filled_count = np.sum(orders_df.iloc[::2]['status'] == 'filled')
sell_filled_count = np.sum(orders_df.iloc[1::2]['status'] == 'filled')
print(f"买单成交数: {buy_filled_count}，成交概率: {buy_filled_count/pair_count:.2%}")
print(f"卖单成交数: {sell_filled_count}，成交概率: {sell_filled_count/pair_count:.2%}")

# 手动画出pnl和持仓随挂单对的变化，pnl单独一个y轴
import matplotlib.pyplot as plt
fig = plt.figure(figsize=(16,6))
ax1 = fig.add_subplot(111)
ax1.plot(position_list, color='tab:blue', label='Position')
ax1.set_xlabel('Order Pair Index')
ax1.set_ylabel('Position', color='tab:blue')
ax1.tick_params(axis='y', labelcolor='tab:blue')

ax2 = ax1.twinx()
ax2.plot(pnl_list, color='tab:red', label='Cumulative PnL')
ax2.set_ylabel('Cumulative PnL', color='tab:red')
ax2.tick_params(axis='y', labelcolor='tab:red')

plt.title('PnL and Position by Order Pair')
fig.tight_layout()
plt.show()

# 再画一幅图看价格和调整后的挂单价格
mid_price_list = df[order_book_col].values
plt.figure(figsize=(16,6))
plt.plot(mid_price_list, label='Mid Price')
plt.xlabel('Order Pair Index')
plt.ylabel('Price')
plt.title('Mid Price by Order Pair')
plt.legend()
plt.tight_layout()
plt.show()